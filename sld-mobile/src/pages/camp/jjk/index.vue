<template>
  <view class="camp-page" :style="mix_diyStyle">
    <!-- 页面背景 -->
    <view class="page-background">
      <image
        class="bg-image"
        src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_bg.png"
        mode="aspectFill"
      />
    </view>

    <!-- 粒子效果背景 -->
    <canvas
      id="particleCanvas"
      canvas-id="particleCanvas"
      class="particle-canvas"
    ></canvas>

    <view class="container">
      <!-- 卡片区域 -->
      <view class="cards-container">
        <!-- 左侧卡片 -->
        <view class="side-card left-card">
          <image
            class="side-card-image"
            src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>

        <!-- 中间主卡片 -->
        <view class="main-card" :class="{ 'card-flip': isCardDrawn || isFlipping, 'card-flipping': isFlipping }" @click="handleCardClick">
          <!-- 卡片背面（默认显示） -->
          <view class="card-back">
            <image
              class="main-card-image"
              :src="cardBackImage"
              mode="aspectFit"
            />
          </view>
          <!-- 卡片正面（翻转后显示） -->
          <view class="card-front">
            <image
              class="main-card-image"
              :src="drawnCardImage"
              mode="aspectFit"
              v-if="drawnCardImage"
            />
            <!-- 镭射彩虹光效果层 -->
            <view class="holographic-effect" v-if="isFlipping || isCardDrawn"></view>
          </view>
        </view>

        <!-- 右侧卡片 -->
        <view class="side-card right-card">
          <image
            class="side-card-image"
            src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_side_bg.png"
            mode="aspectFit"
          />
        </view>
      </view>

      <!-- 抽取按钮 -->
      <view class="draw-button-container">
        <image
          class="draw-button"
          src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_button.png"
          mode="widthFix"
          @click="drawCard"
          :class="{ 'disabled': isDrawing }"
        />
        <!-- <view class="button-text" v-if="isDrawing">抽取中...</view> -->
      </view>

      <!-- 分享按钮 - 只在抽取成功后显示 -->
      <view class="share-button-container" v-if="isCardDrawn && drawnCardImage">
        <image
          class="share-button-image"
          src="http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/camp_share_button.png"
          @click="generatePoster"
          :class="{ 'disabled': isGeneratingPoster }"
          mode="aspectFit"
        />
      </view>

      <!-- 调试信息 -->
      <!-- <view style="position: fixed; top: 100rpx; left: 20rpx; background: rgba(0,0,0,0.8); color: white; padding: 10rpx; font-size: 24rpx; z-index: 999;">
        isCardDrawn: {{ isCardDrawn }}<br>
        drawnCardImage: {{ !!drawnCardImage }}<br>
        currentRid: {{ currentRid }}
      </view> -->

      <!-- 底部Banner区域 -->
      <view class="banner-placeholder">
        <view class="banner-content">
          <text class="banner-text">预留banner位</text>
        </view>
      </view>
    </view>

    <!-- 登录弹窗 -->
    <loginPop ref="loginPop"></loginPop>

    <!-- 海报分享弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-content" @click.stop>
        <view class="poster-header">
          <text class="poster-title">分享海报</text>
          <text class="close-btn" @click="closePosterModal">×</text>
        </view>
        <view class="poster-canvas-container">
          <canvas
            canvas-id="posterCanvas"
            class="poster-canvas"
            :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
          ></canvas>
        </view>
        <view class="poster-actions">
          <view class="action-btn save-btn" @click="savePoster">保存到相册</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import loginPop from '@/components/loginPop/loginPop.vue'

export default {
  components: {
    loginPop
  },
  data() {
    return {
      // 卡片背面固定图片
      cardBackImage: 'http://sg-mall-prod.oss-accelerate.aliyuncs.com/%20sg/card_bg.png',
      // 抽取到的卡片图片（正面）
      drawnCardImage: null,
      isDrawing: false,
      isFlipping: false,
      // 卡片是否已经被抽取（决定显示正面还是背面）
      isCardDrawn: false,
      currentRid: null,
      groupKey: 'NzfYPDDe', // 彩虹卡资源组
      isGeneratingPoster: false,
      showPosterModal: false,
      canvasWidth: 375,
      canvasHeight: 667,
      posterImagePath: '',
      resourceData: null, // 存储抽到的资源数据
      // Canvas粒子效果数据
      canvas: null,
      ctx: null,
      canvasWidth: 375,
      canvasHeight: 667,
      stars: [],
      starCount: 0,
      maxStars: 100,
      hue: 45, // 金色色调
      animationId: null
    }
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  onLoad(options) {
    setTimeout(() => {
      uni.setNavigationBarTitle({
        title: '潜动力金句卡'
      })
    }, 0)

    // 检查是否有rid参数
    if (options.rid) {
      this.currentRid = options.rid
      this.loadCardByRid(options.rid)
    }
    // 背面始终显示固定图片，不需要额外设置

    // 初始化粒子效果
    this.initParticles()
  },
  onShow() {
    // #ifdef H5
    // H5端检查URL参数中的rid
    if (this.$Route && this.$Route.query && this.$Route.query.rid) {
      const rid = this.$Route.query.rid
      if (rid !== this.currentRid) {
        this.currentRid = rid
        this.loadCardByRid(rid)
      }
    }
    // #endif

    this.checkLogin()
  },
  onUnload() {
    // 清理动画
    this.stopAnimation()
  },
  methods: {
    // 检查登录状态
    checkLogin() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
      }
    },

    // 处理卡片点击 - 如果有预加载的图片，可以翻转查看
    handleCardClick() {
      if (this.isDrawing) return // 抽取中不允许点击

      // 如果有预加载的图片，允许翻转查看
      if (this.drawnCardImage && this.currentRid) {
        this.isCardDrawn = !this.isCardDrawn
      }
    },

    // 初始化粒子效果
    initParticles() {
      console.log('开始初始化Canvas粒子效果')

      // 延迟初始化，确保DOM已渲染
      this.$nextTick(() => {
        setTimeout(() => {
          this.initCanvas()
        }, 1000)
      })
    },

    // 初始化Canvas
    initCanvas() {
      console.log('initCanvas 开始执行')
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync()
        this.canvasWidth = systemInfo.windowWidth
        this.canvasHeight = systemInfo.windowHeight

        console.log('屏幕尺寸:', this.canvasWidth, this.canvasHeight)

        // 创建Canvas上下文
        this.ctx = uni.createCanvasContext('particleCanvas', this)
        console.log('Canvas上下文对象:', this.ctx)

        if (this.ctx) {
          console.log('✅ Canvas上下文创建成功')

          // 先绘制一个测试圆形
          this.drawTestCircle()

          // 创建星星
          this.createStars()

          // 开始动画循环
          this.startAnimation()
        } else {
          console.error('❌ Canvas上下文创建失败')
        }
      } catch (error) {
        console.error('❌ Canvas初始化失败:', error)
      }
    },

    // 绘制测试圆形
    drawTestCircle() {
      console.log('绘制测试圆形')
      this.ctx.setFillStyle('#FF0000')
      this.ctx.beginPath()
      this.ctx.arc(100, 100, 30, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.draw()
      console.log('测试圆形绘制完成')
    },

    // 创建星星
    createStars() {
      this.stars = []
      this.starCount = 0

      for (let i = 0; i < this.maxStars; i++) {
        this.createStar()
      }

      console.log('创建了', this.stars.length, '个星星')
    },

    // 创建单个星星
    createStar() {
      const star = {
        orbitRadius: this.random(this.maxOrbit(this.canvasWidth, this.canvasHeight)),
        radius: this.random(1, 2), // 1-2像素
        orbitX: this.canvasWidth / 2,
        orbitY: this.canvasHeight / 2,
        timePassed: this.random(0, this.maxStars),
        speed: 0,
        alpha: this.random(2, 10) / 10
      }

      star.speed = this.random(star.orbitRadius) / 50000

      this.starCount++
      this.stars[this.starCount] = star
    },

    // 开始动画循环
    startAnimation() {
      console.log('开始动画循环')

      // 使用setInterval代替requestAnimationFrame，更稳定
      this.animationId = setInterval(() => {
        if (!this.ctx) return

        try {
          // 清除画布
          this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

          // 绘制背景
          this.ctx.setFillStyle('rgba(0, 0, 20, 0.1)')
          this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

          // 绘制星星
          for (let i = 1; i < this.stars.length; i++) {
            if (this.stars[i]) {
              this.drawStar(this.stars[i])
            }
          }

          this.ctx.draw()
        } catch (error) {
          console.error('动画绘制错误:', error)
        }
      }, 16) // 约60FPS
    },

    // 绘制星星
    drawStar(star) {
      const x = Math.sin(star.timePassed) * star.orbitRadius + star.orbitX
      const y = Math.cos(star.timePassed) * star.orbitRadius + star.orbitY
      const twinkle = this.random(30)

      // 闪烁效果
      if (twinkle === 1 && star.alpha > 0.2) {
        star.alpha -= 0.02
      } else if (twinkle === 2 && star.alpha < 0.8) {
        star.alpha += 0.02
      }

      // 计算当前透明度
      const alpha = Math.max(0.2, Math.min(1, star.alpha))

      // 绘制星星（简化版本）
      this.ctx.setFillStyle(`rgba(255, 215, 0, ${alpha})`)
      this.ctx.beginPath()
      this.ctx.arc(x, y, star.radius, 0, Math.PI * 2)
      this.ctx.fill()

      // 中心亮点（只有2像素的星星才有亮点）
      if (star.radius >= 2) {
        this.ctx.setFillStyle(`rgba(255, 255, 255, ${alpha * 0.8})`)
        this.ctx.beginPath()
        this.ctx.arc(x, y, star.radius * 0.4, 0, Math.PI * 2)
        this.ctx.fill()
      }

      star.timePassed += star.speed
    },

    // 工具函数：随机数
    random(min, max) {
      if (arguments.length < 2) {
        max = min
        min = 0
      }

      if (min > max) {
        const hold = max
        max = min
        min = hold
      }

      return Math.floor(Math.random() * (max - min + 1)) + min
    },

    // 工具函数：最大轨道半径
    maxOrbit(x, y) {
      const max = Math.max(x, y)
      const diameter = Math.round(Math.sqrt(max * max + max * max))
      return diameter / 2
    },

    // 创建爆发粒子效果
    createBurstParticles() {
      if (!this.ctx) return

      console.log('创建爆发粒子效果')

      // 临时增加更多亮星
      const burstCount = 30
      const centerX = this.canvasWidth / 2
      const centerY = this.canvasHeight * 0.45 // 卡片位置

      for (let i = 0; i < burstCount; i++) {
        const angle = (360 / burstCount) * i
        const distance = this.random(50, 150)

        const burstStar = {
          orbitRadius: distance,
          radius: this.random(1, 2), // 爆发粒子也是1-2像素
          orbitX: centerX,
          orbitY: centerY,
          timePassed: angle * Math.PI / 180,
          speed: this.random(100, 300) / 100000,
          alpha: 1,
          isBurst: true,
          life: 60 // 60帧生命周期
        }

        this.stars.push(burstStar)
      }

      // 1秒后清理爆发粒子
      setTimeout(() => {
        this.stars = this.stars.filter(star => !star.isBurst)
        console.log('清理爆发粒子，剩余星星数量:', this.stars.length)
      }, 1000)
    },

    // 停止动画
    stopAnimation() {
      if (this.animationId) {
        clearInterval(this.animationId)
        this.animationId = null
      }
    },

    // 根据rid加载对应卡片
    loadCardByRid(rid) {
      if (!rid) return

      // 根据技术方案，rid格式应该是 groupKey + id，比如 NzfYPDDe01
      // 这里我们先简单处理，直接根据rid构造图片URL
      // 实际项目中应该调用接口获取资源信息

      // 临时处理：如果rid是数字，则使用对应的图片
      if (/^\d+$/.test(rid)) {
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
        this.currentRid = rid
        // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
        this.isCardDrawn = true
      } else {
        // 调用资源接口获取具体资源信息
        this.$request({
          url: 'front/camp/resource',
          method: 'GET',
          data: {
            groupKey: this.groupKey,
            rid: rid
          }
        }).then(res => {
          if (res.state === 200 && res.data) {
            this.resourceData = res.data
            this.drawnCardImage = res.data.imageUrl
            this.currentRid = rid
            // 通过URL参数加载的卡片，显示为已抽取状态，这样分享按钮会显示
            this.isCardDrawn = true
          } else {
            this.$api.msg(res.msg || '获取资源失败')
            this.drawnCardImage = null
            this.isCardDrawn = false
          }
        }).catch(err => {
          console.error('加载资源失败:', err)
          this.drawnCardImage = null
          this.isCardDrawn = false
        })
      }
    },
    
    // 生成海报
    generatePoster() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }
      
      if (this.isGeneratingPoster) {
        return
      }
      
      this.isGeneratingPoster = true
      
      uni.showLoading({
        title: '生成海报中...'
      })
      
      // 模拟生成海报
      setTimeout(() => {
        this.drawPosterCanvas()
      }, 500)
    },
    
    // 绘制海报画布
    drawPosterCanvas() {
      const ctx = uni.createCanvasContext('posterCanvas', this)
      
      // 设置背景色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      
      // 绘制标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 24px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('金句卡分享', this.canvasWidth / 2, 50)
      
      // 绘制卡片图片
      if (this.drawnCardImage && this.currentRid) {
        uni.getImageInfo({
          src: this.drawnCardImage,
          success: (res) => {
            const imgWidth = 200
            const imgHeight = 280
            const imgX = (this.canvasWidth - imgWidth) / 2
            const imgY = 80
            
            ctx.drawImage(res.path, imgX, imgY, imgWidth, imgHeight)
            
            // 绘制卡片编号
            ctx.fillStyle = '#666666'
            ctx.font = '16px sans-serif'
            ctx.fillText(`卡片编号: ${this.currentRid}`, this.canvasWidth / 2, imgY + imgHeight + 30)
            
            // 绘制底部文字
            ctx.fillStyle = '#999999'
            ctx.font = '14px sans-serif'
            ctx.fillText('扫码体验更多精彩内容', this.canvasWidth / 2, this.canvasHeight - 50)
            
            ctx.draw(false, () => {
              this.isGeneratingPoster = false
              this.showPosterModal = true
              uni.hideLoading()
            })
          },
          fail: () => {
            this.isGeneratingPoster = false
            uni.hideLoading()
            this.$api.msg('生成海报失败')
          }
        })
      } else {
        this.isGeneratingPoster = false
        uni.hideLoading()
        this.$api.msg('请先抽取卡片')
      }
    },
    
    // 保存海报
    savePoster() {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              this.$api.msg('海报已保存到相册')
              this.closePosterModal()
            },
            fail: () => {
              this.$api.msg('保存失败，请检查相册权限')
            }
          })
        },
        fail: () => {
          this.$api.msg('生成图片失败')
        }
      })
    },
    
    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
    },
    
    // 抽卡函数
    drawCard() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }

      if (this.isDrawing) {
        return
      }

      this.isDrawing = true
      // 重置卡片状态，确保从背面开始
      this.isCardDrawn = false
      this.isFlipping = false

      // 触发爆发粒子效果
      this.createBurstParticles()

      // 先尝试调用真实接口，如果失败则使用模拟数据
      this.$request({
        url: 'front/camp/resource',
        method: 'POST',
        data: {
          groupKey: this.groupKey
          // 不传rid参数，让服务端随机返回
        }
      }).then(res => {
        if (res.state === 200 && res.data) {
          const resourceData = res.data
          const rid = resourceData.rid

          // 更新rid到URL参数
          this.updateUrlWithRid(rid)

          // 模拟抽卡动画
          setTimeout(() => {
            // 设置抽取到的图片数据
            this.resourceData = resourceData
            this.drawnCardImage = resourceData.imageUrl
            this.currentRid = rid

            // 开始翻转动画
            this.isFlipping = true

            // 翻转动画完成后显示正面
            setTimeout(() => {
              this.isCardDrawn = true
              this.isFlipping = false
              this.isDrawing = false
              this.$api.msg('恭喜获得金句卡！')
            }, 400) // 翻转动画时间
          }, 800)
        } else {
          this.handleDrawCardFallback()
        }
      }).catch(err => {
        console.error('抽卡接口调用失败，使用模拟数据:', err)
        this.handleDrawCardFallback()
      })
    },

    // 抽卡失败时的备用处理
    handleDrawCardFallback() {
      // 生成500-600之间的随机数作为演示
      const cardNumber = Math.floor(Math.random() * 100) + 500

      // 更新rid到URL参数
      this.updateUrlWithRid(cardNumber)

      // 模拟抽卡动画
      setTimeout(() => {
        // 设置抽取到的图片数据
        this.drawnCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`
        this.currentRid = cardNumber

        // 开始翻转动画
        this.isFlipping = true

        // 翻转动画完成后显示正面
        setTimeout(() => {
          this.isCardDrawn = true
          this.isFlipping = false
          this.isDrawing = false
          this.$api.msg('恭喜获得金句卡！')
        }, 400) // 翻转动画时间
      }, 800)
    },
    
    // 更新URL参数
    updateUrlWithRid(rid) {
      // #ifdef H5
      const currentUrl = new URL(window.location.href)
      currentUrl.searchParams.set('rid', rid)
      window.history.replaceState({}, '', currentUrl.toString())
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序端可以考虑使用其他方式保存状态，比如本地存储
      uni.setStorageSync('current_card_rid', rid)
      // #endif
    },
    
    // 跳转商品链接
    goToProduct() {
      // #ifdef H5
      window.open('https://baidu.com', '_blank')
      // #endif
      // #ifndef H5
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent('https://baidu.com')}`
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.camp-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bg-image {
    width: 100%;
    height: 100%;
  }
}

.particle-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 120rpx 20rpx 200rpx;
}

.cards-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 800rpx;
  margin-bottom: 40rpx;
  perspective: 1000rpx;
}

.side-card {
  position: absolute;
  width: 320rpx;
  height: 500rpx;
  z-index: 1;
  animation: cardFloat 3s ease-in-out infinite;

  &.left-card {
    left: 20rpx;
    --rotate-y: -15deg;
    --rotate-z: -5deg;
    transform: rotateY(-15deg) rotateZ(-5deg);
    animation-delay: 0s;
  }

  &.right-card {
    right: 20rpx;
    --rotate-y: 15deg;
    --rotate-z: 5deg;
    transform: rotateY(15deg) rotateZ(5deg);
    animation-delay: 1.5s;
  }

  .side-card-image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.4);
  }
}

.main-card {
  position: relative;
  z-index: 2;
  width: 440rpx;
  height: 776rpx;
  transition: transform 0.8s ease-in-out;
  transform-style: preserve-3d;

  &.card-flip {
    transform: rotateY(180deg);

    // 翻转后静态显示，没有动画
    .holographic-effect {
      &::before {
        opacity: 0;
      }

      &::after {
        opacity: 0;
      }
    }
  }

  &.card-flipping {
    // 翻转时的镭射光效果
    filter: drop-shadow(0 0 30rpx rgba(255, 215, 0, 0.6));

    .holographic-effect {
      &::before {
        animation: rainbowFlip 0.8s ease-in-out;
        opacity: 1;
        filter: blur(0.5px) brightness(1.5) contrast(2);
      }

      &::after {
        animation: sparkleFlip 0.8s ease-in-out;
        opacity: 1;
        filter: brightness(1.5) contrast(1.5);
      }
    }
  }

  .card-back,
  .card-front {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    overflow: hidden;
  }

  .card-back {
    .main-card-image {
      width: 120%;
      height: 120%;
      margin: -10%;
      border-radius: 0;
      box-shadow: none;
      border: none;
    }
  }

  .card-front {
    position: relative;

    .main-card-image {
      width: 100%;
      height: 100%;
      border-radius: 20rpx;
      box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.5);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
    }

    // 镭射彩虹光效果层
    .holographic-effect {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 20rpx;
      pointer-events: none;
      z-index: 1;
      overflow: hidden;

      // 主要的彩虹光束效果
      &::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        right: -50%;
        bottom: -50%;
        background: linear-gradient(
          45deg,
          transparent 30%,
          rgba(255, 0, 150, 0.8) 35%,
          rgba(0, 255, 255, 0.8) 40%,
          rgba(255, 255, 0, 0.8) 45%,
          rgba(255, 0, 255, 0.8) 50%,
          rgba(0, 255, 0, 0.8) 55%,
          rgba(255, 100, 0, 0.8) 60%,
          transparent 70%
        );
        background-size: 200% 200%;
        background-position: 0% 0%;
        mix-blend-mode: screen;
        opacity: 0;
        filter: blur(1px) brightness(1.2) contrast(1.5);
        border-radius: 20rpx;
        z-index: 1;
      }

      // 闪光点效果
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.6) 1px, transparent 2px),
          radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.7) 1px, transparent 2px),
          radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.5) 1px, transparent 2px),
          linear-gradient(
            125deg,
            rgba(255, 0, 132, 0.15) 15%,
            rgba(252, 164, 0, 0.12) 30%,
            rgba(255, 255, 0, 0.1) 40%,
            rgba(0, 255, 138, 0.08) 60%,
            rgba(0, 207, 255, 0.12) 70%,
            rgba(204, 76, 250, 0.15) 85%
          );
        background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 160% 160%;
        background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 50% 50%;
        mix-blend-mode: screen;
        opacity: 0;
        filter: brightness(1.3) contrast(1.2);
        border-radius: 20rpx;
        z-index: 2;
      }
    }
  }

  .card-back {
    transform: rotateY(0deg);
  }

  .card-front {
    transform: rotateY(180deg);
  }
}

.draw-button-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 12rpx;

  .draw-button {
    width: 360rpx;
    height: auto;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

    &:active {
      transform: scale(0.9);
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.4));
      transition: all 0.1s ease-out;
    }

    &.disabled {
      opacity: 0.7;
      transform: none;
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.2));
    }
  }

  .button-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 36rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 10;
  }
}

.share-button-container {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  .share-button-image {
    width: 360rpx; // 与抽取按钮相同宽度
    height: auto;
    transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));

    &:active {
      transform: scale(0.9);
      filter: drop-shadow(0 5rpx 15rpx rgba(0, 0, 0, 0.4));
      transition: all 0.1s ease-out;
    }

    &.disabled {
      opacity: 0.7;
      transform: none;
      filter: drop-shadow(0 5rpx 10rpx rgba(0, 0, 0, 0.2));
    }
  }
}

.banner-placeholder {
  position: fixed;
  bottom: 60rpx;
  left: 40rpx;
  right: 40rpx;
  height: 100rpx;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  // border: 1rpx solid rgba(255, 255, 255, 0.1);
  z-index: 10;

  .banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .banner-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

/* 海报弹窗样式 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.poster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.close-btn {
  font-size: 40rpx;
  color: white;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.poster-canvas-container {
  padding: 40rpx;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
}

.poster-canvas {
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.poster-actions {
  padding: 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

/* 动画效果 */
@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg));
  }
  50% {
    transform: translateY(-20rpx) rotateY(var(--rotate-y, 0deg)) rotateZ(var(--rotate-z, 0deg));
  }
}

/* 镭射彩虹光动画 - 只在翻转时触发 */
@keyframes rainbowFlip {
  0% {
    background-position: -100% -100%;
    opacity: 0;
    filter: blur(2px) brightness(1) contrast(1);
  }
  25% {
    background-position: 0% 0%;
    opacity: 0.8;
    filter: blur(1px) brightness(1.5) contrast(2);
  }
  50% {
    background-position: 100% 100%;
    opacity: 1;
    filter: blur(0px) brightness(2) contrast(2.5);
  }
  75% {
    background-position: 200% 200%;
    opacity: 0.6;
    filter: blur(1px) brightness(1.2) contrast(1.8);
  }
  100% {
    background-position: 300% 300%;
    opacity: 0;
    filter: blur(2px) brightness(1) contrast(1);
  }
}

@keyframes sparkleFlip {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 50% 50%;
    opacity: 0;
    filter: brightness(1) contrast(1);
  }
  25% {
    background-position: 25% 25%, 75% 25%, 25% 75%, 75% 75%, 40% 40%;
    opacity: 0.8;
    filter: brightness(1.5) contrast(1.5);
  }
  50% {
    background-position: 50% 50%, 50% 50%, 50% 50%, 50% 50%, 60% 60%;
    opacity: 1;
    filter: brightness(2) contrast(2);
  }
  75% {
    background-position: 75% 75%, 25% 75%, 75% 25%, 25% 25%, 45% 45%;
    opacity: 0.6;
    filter: brightness(1.3) contrast(1.3);
  }
  100% {
    background-position: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 70% 70%;
    opacity: 0;
    filter: brightness(1) contrast(1);
  }
}



@keyframes buttonGlow {
  0%, 100% {
    filter: drop-shadow(0 10rpx 20rpx rgba(0, 0, 0, 0.3));
  }
  50% {
    filter: drop-shadow(0 15rpx 30rpx rgba(255, 215, 0, 0.4));
  }
}

.draw-button {
  animation: buttonGlow 2s ease-in-out infinite;
}
</style>
